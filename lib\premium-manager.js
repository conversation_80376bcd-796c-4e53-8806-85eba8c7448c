// lib/premium-manager.js
// Premium Feature Management System for Stashy Extension

/**
 * Premium Feature Manager
 * Handles feature locking/unlocking based on user's premium status
 */
window.StashyPremium = (function() {
    'use strict';

    // Premium feature configuration
    const PREMIUM_FEATURES = {
        // Export features
        EXPORT_PDF: 'export_pdf',
        EXPORT_WORD: 'export_word',
        EXPORT_HTML: 'export_html',
        
        // Google Drive sync
        DRIVE_SYNC: 'drive_sync',
        DRIVE_BACKUP: 'drive_backup',
        
        // Template features
        TEMPLATE_BUILDER: 'template_builder',
        ADVANCED_TEMPLATES: 'advanced_templates',
        CUSTOM_TEMPLATES: 'custom_templates',
        
        // Highlighting features
        ADVANCED_HIGHLIGHTS: 'advanced_highlights',
        HIGHLIGHT_STYLES: 'highlight_styles',
        HIGHLIGHT_CATEGORIES: 'highlight_categories',
        
        // AI features
        AI_FEATURES: 'ai_features',
        AI_ANALYSIS: 'ai_analysis',
        AI_SUMMARIZATION: 'ai_summarization',
        AI_ACADEMIC_SOLVER: 'ai_academic_solver',
        AI_TRANSCRIPT_ANALYSIS: 'ai_transcript_analysis',
        AI_SHOPPING_ASSISTANT: 'ai_shopping_assistant',
        
        // Advanced features
        UNLIMITED_NOTES: 'unlimited_notes',
        UNLIMITED_NOTEBOOKS: 'unlimited_notebooks',
        ADVANCED_SEARCH: 'advanced_search',
        BULK_OPERATIONS: 'bulk_operations',
        TIMESTAMP_SCREENSHOTS: 'timestamp_screenshots',
        EQUATION_EDITOR: 'equation_editor'
    };

    // Free tier limitations
    const FREE_LIMITS = {
        MAX_NOTES: 10,
        MAX_NOTEBOOKS: 2,
        MAX_HIGHLIGHTS: 10
    };

    // Current premium status (cached)
    let currentPremiumStatus = {
        isPremium: false,
        expiryDate: null,
        lastChecked: 0
    };

    // Trial status tracking
    let currentTrialStatus = {
        isTrialActive: false,
        trialStartDate: null,
        trialExpiryDate: null,
        trialUsed: false,
        trialActivatedAt: null
    };

    // Trial configuration
    const TRIAL_CONFIG = {
        DURATION_DAYS: 7,
        DURATION_MS: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
        NOTIFICATION_THRESHOLDS: {
            THREE_DAYS: 3 * 24 * 60 * 60 * 1000, // 3 days before expiry
            ONE_DAY: 1 * 24 * 60 * 60 * 1000,    // 1 day before expiry
            EXPIRED: 0                            // On expiry
        }
    };

    // Feature usage counters for free tier
    let usageCounters = {
        notes: 0,
        notebooks: 0,
        highlights: 0
    };

    /**
     * Initialize the premium manager
     */
    async function initialize() {
        try {
            // Load cached premium status
            const cached = await chrome.storage.local.get('stashy_premium_status');
            if (cached.stashy_premium_status) {
                currentPremiumStatus = cached.stashy_premium_status;
            }

            // Load trial status
            const trialData = await chrome.storage.local.get('stashy_trial_status');
            if (trialData.stashy_trial_status) {
                currentTrialStatus = trialData.stashy_trial_status;
                // Validate trial expiry on initialization
                await validateTrialStatus();
            } else {
                // Check if this is a fresh installation that should get auto-trial
                await checkForAutoTrialActivation();
            }

            // Load usage counters
            const counters = await chrome.storage.local.get('stashy_usage_counters');
            if (counters.stashy_usage_counters) {
                usageCounters = counters.stashy_usage_counters;
            }

            // Check premium status if cache is old (> 15 minutes) - less aggressive
            const now = Date.now();
            if (now - currentPremiumStatus.lastChecked > 900000) {
                await refreshPremiumStatus();
            }

            // Set up periodic trial validation
            if (currentTrialStatus.isTrialActive) {
                scheduleTrialNotifications();
            }


        } catch (error) {
            console.error('Stashy Premium: Error initializing manager:', error);
        }
    }

    /**
     * Check if user is eligible for auto-trial activation
     */
    async function checkForAutoTrialActivation() {
        try {
            // Check if extension was recently installed (within last 5 minutes)
            const installData = await chrome.storage.local.get('stashy_install_timestamp');
            const now = Date.now();

            if (!installData.stashy_install_timestamp) {
                // No install timestamp found, this might be an existing user
                console.log('Stashy Premium: No install timestamp found, skipping auto-trial');
                return;
            }

            const timeSinceInstall = now - installData.stashy_install_timestamp;
            const fiveMinutes = 5 * 60 * 1000;

            if (timeSinceInstall > fiveMinutes) {
                console.log('Stashy Premium: Install timestamp too old, skipping auto-trial');
                return;
            }

            // Check if user already has premium
            if (currentPremiumStatus.isPremium) {
                console.log('Stashy Premium: User already premium, skipping auto-trial');
                return;
            }

            console.log('Stashy Premium: User eligible for auto-trial, activating...');
            await activateTrial();

        } catch (error) {
            console.error('Stashy Premium: Error checking auto-trial eligibility:', error);
        }
    }

    /**
     * Check if user has premium access (including active trial)
     * @returns {boolean} True if user is premium or has active trial
     */
    function isPremium() {
        return currentPremiumStatus.isPremium || currentTrialStatus.isTrialActive;
    }

    /**
     * Check if user has active trial
     * @returns {boolean} True if trial is active
     */
    function isTrialActive() {
        return currentTrialStatus.isTrialActive;
    }

    /**
     * Check if user has used their trial
     * @returns {boolean} True if trial has been used
     */
    function isTrialUsed() {
        return currentTrialStatus.trialUsed;
    }

    /**
     * Check if a specific feature is available
     * @param {string} feature - Feature identifier from PREMIUM_FEATURES
     * @returns {boolean} True if feature is available
     */
    function isFeatureAvailable(feature) {
        // Premium users have access to all features
        if (currentPremiumStatus.isPremium) {
            return true;
        }

        // Trial users have access to all premium features
        if (currentTrialStatus.isTrialActive) {
            return true;
        }

        // Free users have limited access
        switch (feature) {
            case PREMIUM_FEATURES.UNLIMITED_NOTES:
                return usageCounters.notes < FREE_LIMITS.MAX_NOTES;
            case PREMIUM_FEATURES.UNLIMITED_NOTEBOOKS:
                return usageCounters.notebooks < FREE_LIMITS.MAX_NOTEBOOKS;
            case PREMIUM_FEATURES.ADVANCED_HIGHLIGHTS:
                // Allow timestamp screenshots for free users
                return true;
            default:
                // All other features require premium or trial
                return false;
        }
    }

    /**
     * Check if user can perform an action (with usage limits)
     * @param {string} action - Action type ('note', 'notebook', 'highlight')
     * @returns {boolean} True if action is allowed
     */
    function canPerformAction(action) {
        if (currentPremiumStatus.isPremium || currentTrialStatus.isTrialActive) {
            return true;
        }

        switch (action) {
            case 'note':
                return usageCounters.notes < FREE_LIMITS.MAX_NOTES;
            case 'notebook':
                return usageCounters.notebooks < FREE_LIMITS.MAX_NOTEBOOKS;
            case 'highlight':
                return usageCounters.highlights < FREE_LIMITS.MAX_HIGHLIGHTS;
            default:
                return false;
        }
    }

    /**
     * Count actual items in storage for dynamic enforcement
     * @param {string} type - Type of items to count ('notes', 'notebooks', 'highlights' or singular forms)
     * @returns {Promise<number>} Actual count of items in storage
     */
    async function countActualItems(type) {
        try {
            // Normalize to plural form
            const normalizedType = type.endsWith('s') ? type : type + 's';

            switch (normalizedType) {
                case 'notes':
                    return await countNotesInStorage();
                case 'notebooks':
                    return await countNotebooksInStorage();
                case 'highlights':
                    return await countHighlightsInStorage();
                default:
                    return 0;
            }
        } catch (error) {
            console.error(`Stashy Premium: Error counting ${type}:`, error);
            return 0;
        }
    }

    /**
     * Count notes in storage
     * @returns {Promise<number>} Number of notes
     */
    async function countNotesInStorage() {
        try {
            const allStorage = await chrome.storage.local.get(null);
            let noteCount = 0;

            // Count regular notes (pattern: Stashy_note_<url>_note<number>)
            // Count global notes (pattern: Stashy_note_global_note<number>)
            for (const key in allStorage) {
                if (key.startsWith('Stashy_note_') || key.startsWith('Stashy_global_note_')) {
                    const noteData = allStorage[key];
                    // Only count notes that have actual content
                    if (noteData && (noteData.text || noteData.title || noteData.content)) {
                        const hasContent = (noteData.text && noteData.text.trim() !== '') ||
                                         (noteData.title && noteData.title.trim() !== '') ||
                                         (noteData.content && noteData.content.trim() !== '');
                        if (hasContent) {
                            noteCount++;
                        }
                    }
                }
            }

            return noteCount;
        } catch (error) {
            console.error('Stashy Premium: Error counting notes:', error);
            return 0;
        }
    }

    /**
     * Count notebooks in storage
     * @returns {Promise<number>} Number of notebooks
     */
    async function countNotebooksInStorage() {
        try {
            // Try both possible storage keys
            const result1 = await chrome.storage.local.get('stashy_notebooks');
            const result2 = await chrome.storage.local.get('Stashy_notebooks');

            let notebooks = result1.stashy_notebooks || result2.Stashy_notebooks || [];

            // Count only actual notebook objects (not metadata)
            let notebookCount = 0;
            if (Array.isArray(notebooks)) {
                // Filter out any metadata or invalid entries
                notebookCount = notebooks.filter(notebook =>
                    notebook &&
                    typeof notebook === 'object' &&
                    notebook.name &&
                    notebook.name.trim() !== ''
                ).length;
            }

            return notebookCount;
        } catch (error) {
            console.error('Stashy Premium: Error counting notebooks:', error);
            return 0;
        }
    }

    /**
     * Count highlights in storage
     * @returns {Promise<number>} Number of highlights
     */
    async function countHighlightsInStorage() {
        try {
            const allStorage = await chrome.storage.local.get(null);
            let highlightCount = 0;

            // Count highlights (pattern: Stashy_highlights_<url>)
            for (const key in allStorage) {
                if (key.startsWith('Stashy_highlights_')) {
                    const highlightData = allStorage[key];
                    if (Array.isArray(highlightData)) {
                        highlightCount += highlightData.length;
                    }
                }
            }

            return highlightCount;
        } catch (error) {
            console.error('Stashy Premium: Error counting highlights:', error);
            return 0;
        }
    }

    /**
     * Record usage of a feature (for free tier limits)
     * @param {string} action - Action type ('note', 'notebook', 'highlight')
     */
    async function recordUsage(action) {
        if (currentPremiumStatus.isPremium || currentTrialStatus.isTrialActive) {
            return; // No limits for premium or trial users
        }

        switch (action) {
            case 'note':
                usageCounters.notes++;
                break;
            case 'notebook':
                usageCounters.notebooks++;
                break;
            case 'highlight':
                usageCounters.highlights++;
                break;
            default:
                return;
        }

        // Save updated counters
        try {
            await chrome.storage.local.set({ stashy_usage_counters: usageCounters });

            // Dispatch event for UI updates
            document.dispatchEvent(new CustomEvent('stashyUsageCountersChanged', {
                detail: { action, newCount: usageCounters[action], usageCounters }
            }));
        } catch (error) {
            console.error(`Stashy Premium: Error saving usage counters:`, error);
        }
    }

    /**
     * Check if user can create a new item with dynamic enforcement
     * @param {string} type - Type of item ('note', 'notebook', 'highlight')
     * @returns {Promise<Object>} Result object with canCreate boolean and details
     */
    async function canCreateItem(type) {
        // Premium and trial users have unlimited access
        if (currentPremiumStatus.isPremium || currentTrialStatus.isTrialActive) {
            return {
                canCreate: true,
                isPremium: true,
                currentCount: 0,
                limit: 0,
                message: ''
            };
        }

        // Normalize type to plural form for counting
        let countType;
        let limit;
        let itemName;

        switch (type) {
            case 'note':
            case 'notes':
                countType = 'notes';
                limit = FREE_LIMITS.MAX_NOTES;
                itemName = 'notes';
                break;
            case 'notebook':
            case 'notebooks':
                countType = 'notebooks';
                limit = FREE_LIMITS.MAX_NOTEBOOKS;
                itemName = 'notebooks';
                break;
            case 'highlight':
            case 'highlights':
                countType = 'highlights';
                limit = FREE_LIMITS.MAX_HIGHLIGHTS;
                itemName = 'highlights';
                break;
            default:
                return {
                    canCreate: false,
                    isPremium: false,
                    currentCount: 0,
                    limit: 0,
                    message: `Unknown item type: ${type}`
                };
        }

        // Count actual items in storage using the normalized plural form
        const currentCount = await countActualItems(countType);

        const canCreate = currentCount < limit;
        let message = '';

        if (!canCreate) {
            message = `You have ${currentCount}/${limit} ${itemName}. Delete some ${itemName} to create new ones, or upgrade to Stashy Pro for unlimited ${itemName}.`;
        }

        return {
            canCreate,
            isPremium: false,
            currentCount,
            limit,
            message,
            itemName
        };
    }

    /**
     * Show dynamic usage enforcement message
     * @param {Object} checkResult - Result from canCreateItem
     * @param {string} context - Context where the check is performed
     */
    function showUsageEnforcementMessage(checkResult, context = '') {
        if (checkResult.canCreate) {
            return; // No message needed if creation is allowed
        }

        console.log(`Stashy Premium: Showing usage enforcement message for ${checkResult.itemName} in context '${context}'`);

        // Show the blocking message
        if (typeof window.showStatus === 'function') {
            window.showStatus(checkResult.message, 'warning', 8000);
        }

        // Also show upgrade prompt
        if (typeof showUpgradePrompt === 'function') {
            showUpgradePrompt(`Unlimited ${checkResult.itemName}`, `${context}-limit-reached`);
        }

        // Dispatch event for UI updates
        document.dispatchEvent(new CustomEvent('stashyUsageLimitReached', {
            detail: {
                itemType: checkResult.itemName,
                currentCount: checkResult.currentCount,
                limit: checkResult.limit,
                context: context,
                message: checkResult.message
            }
        }));
    }

    /**
     * Get current usage statistics
     * @returns {Object} Usage statistics
     */
    function getUsageStats() {
        // For premium and trial users, show unlimited
        if (currentPremiumStatus.isPremium || currentTrialStatus.isTrialActive) {
            return {
                notes: {
                    used: usageCounters.notes,
                    limit: '∞',
                    remaining: '∞',
                    unlimited: true
                },
                notebooks: {
                    used: usageCounters.notebooks,
                    limit: '∞',
                    remaining: '∞',
                    unlimited: true
                },
                highlights: {
                    used: usageCounters.highlights,
                    limit: '∞',
                    remaining: '∞',
                    unlimited: true
                }
            };
        }

        // For free users, show actual limits
        return {
            notes: {
                used: usageCounters.notes,
                limit: FREE_LIMITS.MAX_NOTES,
                remaining: Math.max(0, FREE_LIMITS.MAX_NOTES - usageCounters.notes),
                unlimited: false
            },
            notebooks: {
                used: usageCounters.notebooks,
                limit: FREE_LIMITS.MAX_NOTEBOOKS,
                remaining: Math.max(0, FREE_LIMITS.MAX_NOTEBOOKS - usageCounters.notebooks),
                unlimited: false
            },
            highlights: {
                used: usageCounters.highlights,
                limit: FREE_LIMITS.MAX_HIGHLIGHTS,
                remaining: Math.max(0, FREE_LIMITS.MAX_HIGHLIGHTS - usageCounters.highlights),
                unlimited: false
            }
        };
    }

    /**
     * Get current usage counts (simplified version for dashboard)
     * @returns {Object} Simple usage counts
     */
    function getUsageCounts() {
        return {
            notes: usageCounters.notes,
            notebooks: usageCounters.notebooks,
            highlights: usageCounters.highlights
        };
    }

    /**
     * Get premium status object
     * @returns {Object} Premium status with helper functions
     */
    function getPremiumStatus() {
        return {
            isPremium: currentPremiumStatus.isPremium,
            expiryDate: currentPremiumStatus.expiryDate,
            lastChecked: currentPremiumStatus.lastChecked,
            isFeatureAvailable: isFeatureAvailable,
            usageCounts: getUsageCounts(),
            // Trial information
            isTrialActive: currentTrialStatus.isTrialActive,
            trialExpiryDate: currentTrialStatus.trialExpiryDate,
            trialUsed: currentTrialStatus.trialUsed
        };
    }

    // === TRIAL MANAGEMENT FUNCTIONS ===

    /**
     * Activate free trial for the user
     * @returns {Promise<Object>} Trial activation result
     */
    async function activateTrial() {
        // Check if trial has already been used
        if (currentTrialStatus.trialUsed) {
            return {
                success: false,
                error: 'Trial has already been used',
                code: 'TRIAL_ALREADY_USED'
            };
        }

        // Check if user is already premium
        if (currentPremiumStatus.isPremium) {
            return {
                success: false,
                error: 'User already has premium access',
                code: 'ALREADY_PREMIUM'
            };
        }

        // Check if trial is already active
        if (currentTrialStatus.isTrialActive) {
            return {
                success: false,
                error: 'Trial is already active',
                code: 'TRIAL_ALREADY_ACTIVE'
            };
        }

        try {
            const now = Date.now();
            const expiryDate = now + TRIAL_CONFIG.DURATION_MS;

            // Update trial status
            currentTrialStatus = {
                isTrialActive: true,
                trialStartDate: now,
                trialExpiryDate: expiryDate,
                trialUsed: true,
                trialActivatedAt: now
            };

            // Save to storage
            await chrome.storage.local.set({ stashy_trial_status: currentTrialStatus });

            console.log('Stashy Premium: Trial activated successfully', currentTrialStatus);

            // Dispatch event for UI updates
            document.dispatchEvent(new CustomEvent('stashyTrialActivated', {
                detail: {
                    trialExpiryDate: expiryDate,
                    daysRemaining: TRIAL_CONFIG.DURATION_DAYS
                }
            }));

            // Schedule trial notifications
            scheduleTrialNotifications();

            return {
                success: true,
                trialExpiryDate: expiryDate,
                daysRemaining: TRIAL_CONFIG.DURATION_DAYS,
                message: `7-day free trial activated! Enjoy unlimited access to all premium features.`
            };

        } catch (error) {
            console.error('Stashy Premium: Error activating trial:', error);
            return {
                success: false,
                error: 'Failed to activate trial',
                code: 'ACTIVATION_ERROR'
            };
        }
    }

    /**
     * Validate current trial status and expire if needed
     */
    async function validateTrialStatus() {
        if (!currentTrialStatus.isTrialActive) {
            return;
        }

        const now = Date.now();
        const timeRemaining = currentTrialStatus.trialExpiryDate - now;

        console.log(`Stashy Premium: Validating trial - time remaining: ${timeRemaining}ms`);

        if (timeRemaining <= 0) {
            console.log('Stashy Premium: Trial has expired - deactivating');
            await expireTrial();
        } else {
            // Check for notification thresholds
            checkTrialNotifications(timeRemaining);
        }
    }

    /**
     * Expire the trial and revert to free tier
     */
    async function expireTrial() {
        console.log('Stashy Premium: Expiring trial');

        currentTrialStatus.isTrialActive = false;

        try {
            // Save updated status
            await chrome.storage.local.set({ stashy_trial_status: currentTrialStatus });

            // Dispatch event for UI updates
            document.dispatchEvent(new CustomEvent('stashyTrialExpired', {
                detail: {
                    trialUsed: currentTrialStatus.trialUsed,
                    message: 'Your 7-day free trial has expired. Upgrade to Stashy Pro to continue enjoying premium features!'
                }
            }));

            // Show trial expiry notification
            showTrialExpiryNotification();

            console.log('Stashy Premium: Trial expired successfully');
        } catch (error) {
            console.error('Stashy Premium: Error expiring trial:', error);
        }
    }

    /**
     * Get trial status information
     * @returns {Object} Trial status details
     */
    function getTrialStatus() {
        if (!currentTrialStatus.isTrialActive) {
            return {
                isActive: false,
                isUsed: currentTrialStatus.trialUsed,
                canActivate: !currentTrialStatus.trialUsed && !currentPremiumStatus.isPremium,
                daysRemaining: 0,
                hoursRemaining: 0,
                timeRemaining: 0
            };
        }

        const now = Date.now();
        const timeRemaining = Math.max(0, currentTrialStatus.trialExpiryDate - now);
        const daysRemaining = Math.ceil(timeRemaining / (24 * 60 * 60 * 1000));
        const hoursRemaining = Math.ceil(timeRemaining / (60 * 60 * 1000));

        return {
            isActive: true,
            isUsed: currentTrialStatus.trialUsed,
            canActivate: false,
            daysRemaining,
            hoursRemaining,
            timeRemaining,
            expiryDate: currentTrialStatus.trialExpiryDate,
            startDate: currentTrialStatus.trialStartDate
        };
    }

    /**
     * Check if trial notifications should be shown
     * @param {number} timeRemaining - Time remaining in milliseconds
     */
    function checkTrialNotifications(timeRemaining) {
        const thresholds = TRIAL_CONFIG.NOTIFICATION_THRESHOLDS;
        const now = Date.now();

        // Check if we've already shown notifications to avoid spam
        chrome.storage.local.get('stashy_last_trial_notification').then(result => {
            const lastNotification = result.stashy_last_trial_notification || 0;
            const timeSinceLastNotification = now - lastNotification;
            const oneHour = 60 * 60 * 1000;

            // Only show notifications if it's been at least 1 hour since last one
            if (timeSinceLastNotification < oneHour) {
                return;
            }

            // Check for 3-day warning
            if (timeRemaining <= thresholds.THREE_DAYS && timeRemaining > thresholds.ONE_DAY) {
                showTrialNotification('3-day-warning', {
                    title: 'Trial Ending Soon',
                    message: 'Your free trial expires in 3 days. Upgrade to Stashy Pro to keep your premium features!',
                    daysRemaining: 3
                });
                chrome.storage.local.set({ stashy_last_trial_notification: now });

                // Show browser notification
                if (typeof chrome !== 'undefined' && chrome.notifications) {
                    chrome.notifications.create('trial-3day-warning', {
                        type: 'basic',
                        iconUrl: 'icon48.png',
                        title: 'Stashy Trial Ending Soon',
                        message: 'Your free trial expires in 3 days. Upgrade to keep your premium features!'
                    });
                }
            }

            // Check for 1-day warning
            else if (timeRemaining <= thresholds.ONE_DAY && timeRemaining > 0) {
                showTrialNotification('1-day-warning', {
                    title: 'Trial Expires Tomorrow',
                    message: 'Your free trial expires in 1 day. Upgrade now to avoid losing access to premium features!',
                    daysRemaining: 1
                });
                chrome.storage.local.set({ stashy_last_trial_notification: now });

                // Show browser notification
                if (typeof chrome !== 'undefined' && chrome.notifications) {
                    chrome.notifications.create('trial-1day-warning', {
                        type: 'basic',
                        iconUrl: 'icon48.png',
                        title: 'Stashy Trial Expires Tomorrow',
                        message: 'Your free trial expires in 1 day. Upgrade now to keep premium features!'
                    });
                }
            }

            // Check for final hours warning (less than 6 hours)
            else if (timeRemaining <= 6 * 60 * 60 * 1000 && timeRemaining > 0) {
                const hoursRemaining = Math.ceil(timeRemaining / (60 * 60 * 1000));
                showTrialNotification('final-hours-warning', {
                    title: 'Trial Expires Soon',
                    message: `Your free trial expires in ${hoursRemaining} hour${hoursRemaining !== 1 ? 's' : ''}. Upgrade now to keep your premium features!`,
                    daysRemaining: 0,
                    hoursRemaining
                });
                chrome.storage.local.set({ stashy_last_trial_notification: now });

                // Show browser notification
                if (typeof chrome !== 'undefined' && chrome.notifications) {
                    chrome.notifications.create('trial-final-hours', {
                        type: 'basic',
                        iconUrl: 'icon48.png',
                        title: 'Stashy Trial Expires Soon',
                        message: `Your trial expires in ${hoursRemaining} hour${hoursRemaining !== 1 ? 's' : ''}. Upgrade now!`
                    });
                }
            }
        }).catch(error => {
            console.error('Stashy Premium: Error checking trial notifications:', error);
        });
    }

    /**
     * Schedule trial notifications
     */
    function scheduleTrialNotifications() {
        console.log('Stashy Premium: Scheduling trial notifications');

        // Set up periodic checks for trial expiry
        setInterval(() => {
            if (currentTrialStatus.isTrialActive) {
                validateTrialStatus();
            }
        }, 60 * 60 * 1000); // Check every hour
    }

    /**
     * Show trial notification
     * @param {string} type - Notification type
     * @param {Object} data - Notification data
     */
    function showTrialNotification(type, data) {
        console.log(`Stashy Premium: Showing trial notification - ${type}`, data);

        // Dispatch event for UI handling
        document.dispatchEvent(new CustomEvent('stashyTrialNotification', {
            detail: { type, ...data }
        }));

        // Show notification using existing system
        if (typeof window.showStatus === 'function') {
            window.showStatus(data.message, 'warning', 8000);
        }
    }

    /**
     * Show trial expiry notification with conversion prompt
     */
    function showTrialExpiryNotification() {
        console.log('Stashy Premium: Showing trial expiry notification');

        const message = 'Your 7-day free trial has expired. Upgrade to Stashy Pro to continue enjoying unlimited notes, AI features, and more!';

        // Show upgrade prompt
        if (typeof showUpgradePrompt === 'function') {
            showUpgradePrompt('Trial Expired - Upgrade Now', 'trial-expiry');
        } else {
            // Fallback notification
            if (typeof window.showStatus === 'function') {
                window.showStatus(message, 'error', 10000);
            }
        }
    }

    /**
     * Refresh premium status from background script
     * @param {boolean} showLoading - Whether to show loading indicator
     */
    async function refreshPremiumStatus(showLoading = false) {
        console.log('Stashy Premium: Refreshing premium status...');

        if (showLoading) {
            // Dispatch loading event
            document.dispatchEvent(new CustomEvent('stashyPremiumStatusLoading', {
                detail: { isLoading: true }
            }));
        }

        try {
            const status = await chrome.runtime.sendMessage({ action: 'getPremiumStatus' });

            currentPremiumStatus = {
                isPremium: status.isPremium || false,
                expiryDate: status.expiryDate || null,
                lastChecked: Date.now()
            };

            // Save to storage
            await chrome.storage.local.set({ stashy_premium_status: currentPremiumStatus });

            console.log('Stashy Premium: Status refreshed successfully', currentPremiumStatus);

            // Dispatch success event for UI updates
            document.dispatchEvent(new CustomEvent('stashyPremiumStatusChanged', {
                detail: { ...currentPremiumStatus, success: true }
            }));

            return currentPremiumStatus;
        } catch (error) {
            console.error('Stashy Premium: Error refreshing status:', error);

            // Dispatch error event
            document.dispatchEvent(new CustomEvent('stashyPremiumStatusError', {
                detail: {
                    error: error.message || 'Failed to refresh premium status',
                    fallbackStatus: currentPremiumStatus
                }
            }));

            return currentPremiumStatus;
        } finally {
            if (showLoading) {
                // Dispatch loading complete event
                document.dispatchEvent(new CustomEvent('stashyPremiumStatusLoading', {
                    detail: { isLoading: false }
                }));
            }
        }
    }

    /**
     * Show upgrade prompt for locked features
     * @param {string} feature - Feature that requires upgrade
     * @param {string} context - Context where the prompt is shown
     */
    function showUpgradePrompt(feature, context = '') {
        console.log(`Stashy Premium: Showing upgrade prompt for feature '${feature}' in context '${context}'`);

        const upgradeUrl = 'https://stashyapp.com/pricing.html';

        // Create upgrade notification
        const message = `This feature requires Stashy Pro. Upgrade to unlock ${feature} and more!`;

        console.log(`Stashy Premium: Upgrade message: ${message}`);
        console.log(`Stashy Premium: Upgrade URL: ${upgradeUrl}`);

        // Show notification (you can customize this based on your UI)
        if (typeof window.StashyUI !== 'undefined' && window.StashyUI.showNotification) {
            console.log(`Stashy Premium: Using StashyUI notification system`);
            window.StashyUI.showNotification(message, 'upgrade', {
                action: 'Upgrade Now',
                callback: () => {
                    console.log(`Stashy Premium: Opening upgrade URL: ${upgradeUrl}`);
                    window.open(upgradeUrl, '_blank');
                }
            });
        } else {
            // Fallback to alert
            console.log(`Stashy Premium: Using fallback alert dialog`);
            if (confirm(`${message}\n\nWould you like to upgrade now?`)) {
                console.log(`Stashy Premium: User confirmed upgrade, opening URL: ${upgradeUrl}`);
                try {
                    window.open(upgradeUrl, '_blank');
                    console.log(`Stashy Premium: Successfully opened upgrade URL`);
                } catch (error) {
                    console.error(`Stashy Premium: Error opening upgrade URL:`, error);
                    // Fallback: try to navigate current window
                    try {
                        window.location.href = upgradeUrl;
                    } catch (navError) {
                        console.error(`Stashy Premium: Error navigating to upgrade URL:`, navError);
                    }
                }
            } else {
                console.log(`Stashy Premium: User cancelled upgrade prompt`);
            }
        }
    }

    /**
     * Apply premium styling to UI elements
     * @param {boolean} isPremiumUser - Whether user is premium
     */
    function applyPremiumStyling(isPremiumUser) {
        console.log(`Stashy Premium: Applying premium styling, isPremiumUser: ${isPremiumUser}`);

        const body = document.body;

        if (isPremiumUser) {
            body.classList.add('stashy-premium-user');
            body.classList.remove('stashy-free-user');
            console.log(`Stashy Premium: Applied premium user styling`);
        } else {
            body.classList.add('stashy-free-user');
            body.classList.remove('stashy-premium-user');
            console.log(`Stashy Premium: Applied free user styling`);
        }

        // Update all premium feature elements
        const premiumElements = document.querySelectorAll('[data-premium-feature]');
        console.log(`Stashy Premium: Found ${premiumElements.length} premium feature elements to update`);

        premiumElements.forEach((element, index) => {
            const feature = element.getAttribute('data-premium-feature');
            const isAvailable = isFeatureAvailable(feature);

            console.log(`Stashy Premium: Element ${index + 1} - feature: '${feature}', available: ${isAvailable}`);

            element.classList.toggle('premium-locked', !isAvailable);
            element.classList.toggle('premium-available', isAvailable);

            if (!isAvailable) {
                element.setAttribute('title', 'This feature requires Stashy Pro');
            } else {
                element.removeAttribute('title');
            }
        });
    }

    /**
     * Validate free tier limits are being enforced correctly
     * @returns {Object} Validation results
     */
    function validateFreeTierLimits() {
        console.log(`Stashy Premium: Validating free tier limits enforcement`);

        const validation = {
            isValid: true,
            issues: [],
            currentUsage: { ...usageCounters },
            limits: { ...FREE_LIMITS },
            isPremium: currentPremiumStatus.isPremium
        };

        // Check if usage counters exceed limits for free users
        if (!currentPremiumStatus.isPremium) {
            if (usageCounters.notes > FREE_LIMITS.MAX_NOTES) {
                validation.isValid = false;
                validation.issues.push(`Notes usage (${usageCounters.notes}) exceeds free limit (${FREE_LIMITS.MAX_NOTES})`);
            }

            if (usageCounters.notebooks > FREE_LIMITS.MAX_NOTEBOOKS) {
                validation.isValid = false;
                validation.issues.push(`Notebooks usage (${usageCounters.notebooks}) exceeds free limit (${FREE_LIMITS.MAX_NOTEBOOKS})`);
            }

            if (usageCounters.highlights > FREE_LIMITS.MAX_HIGHLIGHTS_PER_PAGE) {
                validation.isValid = false;
                validation.issues.push(`Highlights usage (${usageCounters.highlights}) exceeds free limit (${FREE_LIMITS.MAX_HIGHLIGHTS_PER_PAGE})`);
            }
        }

        console.log(`Stashy Premium: Validation result:`, validation);
        return validation;
    }

    /**
     * Reset usage counters (for testing purposes)
     */
    async function resetUsageCounters() {
        console.log(`Stashy Premium: Resetting usage counters`);

        usageCounters = {
            notes: 0,
            notebooks: 0,
            highlights: 0
        };

        try {
            await chrome.storage.local.set({ stashy_usage_counters: usageCounters });
            console.log(`Stashy Premium: Usage counters reset successfully`);

            // Dispatch event for UI updates
            document.dispatchEvent(new CustomEvent('stashyUsageCountersReset', {
                detail: { usageCounters }
            }));
        } catch (error) {
            console.error(`Stashy Premium: Error resetting usage counters:`, error);
        }
    }

    // --- Safe Premium Check Utilities ---
    function safeFeatureCheck(featureId, fallbackValue = false) {
        try {
            return isFeatureAvailable(featureId);
        } catch (error) {
            console.warn(`Stashy Premium: Error checking feature ${featureId}:`, error);
            showConsistentErrorMessage('Feature Check Failed', `Unable to verify access to ${featureId}. Please try again.`);
            return fallbackValue;
        }
    }

    function safeActionCheck(actionType, fallbackValue = true) {
        try {
            return canPerformAction(actionType);
        } catch (error) {
            console.warn(`Stashy Premium: Error checking action ${actionType}:`, error);
            showConsistentErrorMessage('Action Check Failed', `Unable to verify permission for ${actionType}. Please try again.`);
            return fallbackValue; // Default to allowing action if check fails
        }
    }

    function safeUsageRecord(actionType) {
        try {
            return recordUsage(actionType);
        } catch (error) {
            console.warn(`Stashy Premium: Error recording usage for ${actionType}:`, error);
            showConsistentErrorMessage('Usage Recording Failed', `Unable to record usage for ${actionType}. Your limits may not be accurate.`);
            return Promise.resolve(); // Return resolved promise to prevent blocking
        }
    }

    function safePremiumStatus() {
        try {
            return refreshPremiumStatus();
        } catch (error) {
            console.warn('Stashy Premium: Error getting premium status:', error);
            showConsistentErrorMessage('Premium Status Check Failed', 'Unable to verify your premium status. Some features may be temporarily unavailable.');
            return Promise.resolve({
                isPremium: false,
                expiryDate: null,
                lastChecked: 0
            });
        }
    }

    function safeUpgradePrompt(feature, context) {
        try {
            return showUpgradePrompt(feature, context);
        } catch (error) {
            console.warn('Stashy Premium: Error showing upgrade prompt:', error);
            showConsistentErrorMessage('Upgrade Prompt Failed', 'Unable to show upgrade options. Please visit stashyapp.com/pricing directly.');
            // Fallback: open stashyapp.com directly
            try {
                window.open('https://stashyapp.com/pricing.html', '_blank');
            } catch (openError) {
                console.error('Stashy Premium: Failed to open upgrade page:', openError);
            }
        }
    }

    /**
     * Show consistent error messages across all premium features
     * @param {string} title - Error title
     * @param {string} message - Error message
     */
    function showConsistentErrorMessage(title, message) {
        console.error(`Stashy Premium: ${title} - ${message}`);

        // Try to use existing notification system
        if (typeof window.showStatus === 'function') {
            window.showStatus(`${title}: ${message}`, 'error', 5000);
        } else if (typeof window.StashyUI !== 'undefined' && window.StashyUI.showNotification) {
            window.StashyUI.showNotification(message, 'error', { title });
        } else {
            // Fallback to console and simple alert for critical errors
            console.warn(`Stashy Premium: Showing fallback error dialog for: ${title}`);
            // Only show alert for critical errors to avoid spam
            if (title.includes('Premium Status') || title.includes('Feature Check')) {
                setTimeout(() => {
                    alert(`Stashy Premium Error\n\n${title}\n${message}`);
                }, 100);
            }
        }
    }

    /**
     * Apply accessibility attributes to premium UI elements
     */
    function applyAccessibilityAttributes() {
        console.log('Stashy Premium: Applying accessibility attributes');

        // Update premium feature elements with proper ARIA attributes
        const premiumElements = document.querySelectorAll('[data-premium-feature]');
        premiumElements.forEach(element => {
            const feature = element.getAttribute('data-premium-feature');
            const isAvailable = isFeatureAvailable(feature);

            // Add ARIA attributes
            element.setAttribute('aria-label', `${feature} feature`);
            element.setAttribute('aria-describedby', `${feature}-status`);

            if (!isAvailable) {
                element.setAttribute('aria-disabled', 'true');
                element.setAttribute('role', 'button');
                element.setAttribute('tabindex', '0');

                // Add keyboard support for upgrade prompts
                element.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        showUpgradePrompt(feature, 'keyboard-navigation');
                    }
                });
            } else {
                element.removeAttribute('aria-disabled');
                element.removeAttribute('tabindex');
            }
        });

        // Update usage counters with ARIA labels
        const usageElements = document.querySelectorAll('[data-usage-counter]');
        usageElements.forEach(element => {
            const counterType = element.getAttribute('data-usage-counter');
            const stats = getUsageStats();

            if (stats[counterType]) {
                const { used, limit, remaining } = stats[counterType];
                element.setAttribute('aria-label', `${counterType} usage: ${used} of ${limit} used, ${remaining} remaining`);

                if (remaining <= 1) {
                    element.setAttribute('aria-describedby', `${counterType}-warning`);
                }
            }
        });
    }

    // === TESTING FUNCTIONS ===

    /**
     * Testing utilities for trial system verification
     */
    const testingUtils = {
        /**
         * Reset trial system for testing
         */
        async resetTrialForTesting() {
            console.log('Stashy Premium: Resetting trial system for testing');

            currentTrialStatus = {
                isTrialActive: false,
                trialStartDate: null,
                trialExpiryDate: null,
                trialUsed: false,
                trialActivatedAt: null
            };

            await chrome.storage.local.remove(['stashy_trial_status', 'stashy_last_trial_notification', 'stashy_install_timestamp']);
            console.log('Stashy Premium: Trial system reset complete');
        },

        /**
         * Simulate trial expiry for testing
         */
        async simulateTrialExpiry() {
            console.log('Stashy Premium: Simulating trial expiry for testing');

            if (currentTrialStatus.isTrialActive) {
                await expireTrial();
                console.log('Stashy Premium: Trial expiry simulation complete');
            } else {
                console.log('Stashy Premium: No active trial to expire');
            }
        },

        /**
         * Set trial to expire in X minutes for testing notifications
         */
        async setTrialExpiryInMinutes(minutes) {
            console.log(`Stashy Premium: Setting trial to expire in ${minutes} minutes for testing`);

            const now = Date.now();
            const expiryTime = now + (minutes * 60 * 1000);

            currentTrialStatus = {
                ...currentTrialStatus,
                isTrialActive: true,
                trialExpiryDate: expiryTime,
                trialUsed: true
            };

            await chrome.storage.local.set({ stashy_trial_status: currentTrialStatus });

            // Clear any existing alarms and set new one
            chrome.alarms.clear("trialExpiry");
            chrome.alarms.create("trialExpiry", { when: expiryTime });

            console.log(`Stashy Premium: Trial set to expire at ${new Date(expiryTime)}`);
        },

        /**
         * Force trial notifications for testing
         */
        async testTrialNotifications() {
            console.log('Stashy Premium: Testing trial notifications');

            // Test 3-day warning
            await new Promise(resolve => setTimeout(resolve, 100));
            checkTrialNotifications(3 * 24 * 60 * 60 * 1000); // 3 days

            // Test 1-day warning
            await new Promise(resolve => setTimeout(resolve, 100));
            checkTrialNotifications(24 * 60 * 60 * 1000); // 1 day

            // Test final hours warning
            await new Promise(resolve => setTimeout(resolve, 100));
            checkTrialNotifications(3 * 60 * 60 * 1000); // 3 hours

            console.log('Stashy Premium: Trial notification tests complete');
        },

        /**
         * Get current trial system state for debugging
         */
        getTrialDebugInfo() {
            return {
                currentTrialStatus,
                timeRemaining: currentTrialStatus.isTrialActive ?
                    currentTrialStatus.trialExpiryDate - Date.now() : 0,
                daysRemaining: currentTrialStatus.isTrialActive ?
                    Math.ceil((currentTrialStatus.trialExpiryDate - Date.now()) / (24 * 60 * 60 * 1000)) : 0,
                hoursRemaining: currentTrialStatus.isTrialActive ?
                    Math.ceil((currentTrialStatus.trialExpiryDate - Date.now()) / (60 * 60 * 1000)) : 0
            };
        }
    };

    // Public API
    return {
        initialize,
        isPremium,
        isFeatureAvailable,
        canPerformAction,
        recordUsage,
        getUsageStats,
        getUsageCounts,
        getPremiumStatus,
        refreshPremiumStatus,
        showUpgradePrompt,
        applyPremiumStyling,
        applyAccessibilityAttributes,
        validateFreeTierLimits,
        resetUsageCounters,
        showConsistentErrorMessage,
        PREMIUM_FEATURES,
        FREE_LIMITS,
        // Trial functions
        isTrialActive,
        isTrialUsed,
        activateTrial,
        getTrialStatus,
        validateTrialStatus,
        expireTrial,
        TRIAL_CONFIG,
        // Safe utility functions
        safeFeatureCheck,
        safeActionCheck,
        safeUsageRecord,
        safePremiumStatus,
        safeUpgradePrompt,
        // Dynamic usage enforcement functions
        countActualItems,
        canCreateItem,
        showUsageEnforcementMessage,
        // Testing utilities (only available in development)
        testing: testingUtils,

    };
})();

// Initialize when loaded
if (typeof chrome !== 'undefined' && chrome.runtime) {
    window.StashyPremium.initialize().catch(console.error);
}

console.log('Stashy: Premium Manager Loaded');
